import { defaultsDeep, groupBy, keyBy } from 'lodash-es'
import { deviceStatus as deviceStatusDict } from '$/dicts/device/index.js'

/**
 * 获取设备名称
 */
export function getDeviceName(device) {
  return device.product ? device.product.split(':')[1] : '未授权设备'
}

/**
 * 获取备注名称
 */
export function getRemark(deviceId) {
  const value = window.appStore.get('device')?.[deviceId]?.remark
  return value
}

/**
 * 获取历史设备列表
 */
export function getHistoryDevices() {
  const devices = window.appStore.get('device') || {}

  const value = Object.values(devices).map(device => ({
    ...device,
  }))

  return value
}

/**
 * 获取当前连接的设备
 */
export async function getCurrentDevices() {
  const devices = await window.adb.getDeviceList() || []

  return devices.map(device => ({
    ...device,
    id: device.id,
    status: device.type,
    name: getDevice<PERSON>ame(device),
    wifi: ([':', '_adb-tls-connect']).some(item => device.id.includes(item)),
    remark: getRemark(device.id),
  }))
}

/**
 * 合并多个设备的 scrcpy 配置（不立即删除源配置）
 */
function mergeScrcpyConfigs(deviceIds, targetDeviceId) {
  if (deviceIds.length === 0) {
    return []
  }

  const scrcpyConfig = window.appStore.get('scrcpy') || {}
  let mergedConfig = scrcpyConfig[scrcpyConfig] || {}
  const processedIds = []

  // 合并所有源设备的配置
  deviceIds.forEach((deviceId) => {
    if (scrcpyConfig[deviceId]) {
      mergedConfig = defaultsDeep(mergedConfig, scrcpyConfig[deviceId])
      processedIds.push(deviceId)
    }
  })

  // 没有配置需要合并
  if (!Object.keys(mergedConfig).length) {
    return []
  }

  // 如果目标设备没有配置，则使用合并后的配置
  if (!scrcpyConfig[targetDeviceId]) {
    scrcpyConfig[targetDeviceId] = mergedConfig

    // 保存更新后的配置（但不删除源配置）
    window.appStore.set('scrcpy', scrcpyConfig)

    console.log(`Merged scrcpy configs from [${processedIds.join(', ')}] to ${targetDeviceId}`)
  }

  // 返回需要删除的配置 ID 列表
  return processedIds
}

/**
 * 批量删除 scrcpy 配置
 */
function deleteScrcpyConfigs(deviceIds) {
  if (deviceIds.length === 0) {
    return
  }

  const scrcpyConfig = window.appStore.get('scrcpy') || {}
  const deletedIds = []

  deviceIds.forEach((deviceId) => {
    if (scrcpyConfig[deviceId]) {
      delete scrcpyConfig[deviceId]
      deletedIds.push(deviceId)
    }
  })

  if (deletedIds.length > 0) {
    window.appStore.set('scrcpy', scrcpyConfig)
    console.log(`Deleted scrcpy configs: [${deletedIds.join(', ')}]`)
  }
}

/**
 * 合并历史和当前设备列表
 */
export function mergeDevices(historyDevices, currentDevices) {
  const sortModel = deviceStatusDict.reduce((obj, item, index) => {
    obj[item.value] = index
    return obj
  }, {})

  // 分离有线和无线设备
  const historyWiredDevices = historyDevices.filter(device => !device.wifi)
  const historyWirelessDevices = historyDevices.filter(device => device.wifi)
  const currentWiredDevices = currentDevices.filter(device => !device.wifi)
  const currentWirelessDevices = currentDevices.filter(device => device.wifi)

  const mergedDevices = []
  const processedHistoryIds = new Set()

  // 处理有线设备 - 直接合并（无需额外处理）
  const wiredMergeList = Object.values(defaultsDeep(keyBy(currentWiredDevices, 'id'), keyBy(historyWiredDevices, 'id')))

  mergedDevices.push(...wiredMergeList)
  historyWiredDevices.forEach(device => processedHistoryIds.add(device.id))

  // 处理无线设备 - 允许存在多个条目，但需要配置合并和迁移
  // 按 serialNo 分组历史无线设备
  const historyWirelessGroups = groupBy(historyWirelessDevices, 'serialNo')
  const configsToDelete = new Set()

  // 处理每个当前无线设备
  currentWirelessDevices.forEach((currentDevice) => {
    // 查找匹配的历史设备组（相同 serialNo）
    const matchingHistoryDevices = historyWirelessGroups[currentDevice.serialNo] || []

    if (matchingHistoryDevices.length > 0) {
      // 合并历史设备的配置到当前设备
      const historyDeviceIds = matchingHistoryDevices.map(device => device.id)
      console.log('historyDeviceIds', historyDeviceIds)
      console.log('currentDevice.id', currentDevice.id)

      const processedConfigIds = mergeScrcpyConfigs(historyDeviceIds, currentDevice.id)
      processedConfigIds.forEach(id => configsToDelete.add(id))

      // 标记这些历史设备为已处理
      matchingHistoryDevices.forEach(device => processedHistoryIds.add(device.id))
    }

    // 添加当前无线设备（使用 defaultsDeep 合并可能存在的历史设备信息）
    const matchingHistoryDevice = matchingHistoryDevices.find(device => device.id === currentDevice.id)
    const mergedDevice = matchingHistoryDevice
      ? defaultsDeep({}, currentDevice, matchingHistoryDevice)
      : currentDevice

    mergedDevices.push(mergedDevice)
  })

  // 统一删除已处理的配置
  deleteScrcpyConfigs(Array.from(configsToDelete))

  // 添加未被处理的历史无线设备
  historyWirelessDevices.forEach((historyDevice) => {
    if (!processedHistoryIds.has(historyDevice.id)) {
      mergedDevices.push(historyDevice)
    }
  })

  // 排序
  const value = mergedDevices.sort((a, b) => sortModel[a.status] - sortModel[b.status])

  return value
}

/**
 * 保存设备信息到存储
 */
export function saveDevicesToStore(devices) {
  const cleanedDevices = devices
    .filter(device => !['unauthorized'].includes(device.status))
    .map(device => ({
      ...device,
      status: 'offline',
      type: 'offline',
    }))

  window.appStore.set('device', keyBy(cleanedDevices, 'id'))
}
